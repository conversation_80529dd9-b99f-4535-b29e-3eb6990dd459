"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ComponentStyles", {
    enumerable: true,
    get: function() {
        return ComponentStyles;
    }
});
const _tagged_template_literal_loose = require("@swc/helpers/_/_tagged_template_literal_loose");
const _jsxruntime = require("react/jsx-runtime");
const _codeframe = require("../components/code-frame/code-frame");
const _dialog = require("../components/dialog");
const _erroroverlaylayout = require("../components/errors/error-overlay-layout/error-overlay-layout");
const _erroroverlaybottomstack = require("../components/errors/error-overlay-bottom-stack");
const _erroroverlaypagination = require("../components/errors/error-overlay-pagination/error-overlay-pagination");
const _styles = require("../components/overlay/styles");
const _erroroverlayfooter = require("../components/errors/error-overlay-footer/error-overlay-footer");
const _terminal = require("../components/terminal/terminal");
const _toast = require("../components/toast");
const _versionstalenessinfo = require("../components/version-staleness-info/version-staleness-info");
const _builderror = require("../container/build-error");
const _errors = require("../container/errors");
const _runtimeerror = require("../container/runtime-error");
const _copybutton = require("../components/copy-button");
const _callstackframe = require("../components/call-stack-frame/call-stack-frame");
const _devtoolsindicator = require("../components/errors/dev-tools-indicator/dev-tools-indicator");
const _css = require("../../utils/css");
const _editorlink = require("../components/terminal/editor-link");
const _environmentnamelabel = require("../components/errors/environment-name-label/environment-name-label");
const _devtoolsinfo = require("../components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info");
const _turbopackinfo = require("../components/errors/dev-tools-indicator/dev-tools-info/turbopack-info");
const _routeinfo = require("../components/errors/dev-tools-indicator/dev-tools-info/route-info");
const _userpreferences = require("../components/errors/dev-tools-indicator/dev-tools-info/user-preferences");
const _fader = require("../components/fader");
function _templateObject() {
    const data = _tagged_template_literal_loose._([
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n        ",
        "\n      "
    ]);
    _templateObject = function() {
        return data;
    };
    return data;
}
function ComponentStyles() {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("style", {
        children: (0, _css.css)(_templateObject(), _copybutton.COPY_BUTTON_STYLES, _callstackframe.CALL_STACK_FRAME_STYLES, _environmentnamelabel.ENVIRONMENT_NAME_LABEL_STYLES, _styles.styles, _toast.styles, _dialog.styles, _erroroverlaylayout.styles, _erroroverlayfooter.styles, _erroroverlaybottomstack.styles, _erroroverlaypagination.styles, _codeframe.CODE_FRAME_STYLES, _terminal.TERMINAL_STYLES, _editorlink.EDITOR_LINK_STYLES, _builderror.styles, _errors.styles, _runtimeerror.styles, _versionstalenessinfo.styles, _devtoolsindicator.DEV_TOOLS_INDICATOR_STYLES, _devtoolsinfo.DEV_TOOLS_INFO_STYLES, _turbopackinfo.DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES, _routeinfo.DEV_TOOLS_INFO_ROUTE_INFO_STYLES, _userpreferences.DEV_TOOLS_INFO_USER_PREFERENCES_STYLES, _fader.FADER_STYLES)
    });
}

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=component-styles.js.map